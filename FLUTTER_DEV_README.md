# 💣 Bomb Flip Betting Game - Flutter Development Specification

## 📱 Project Overview

**Target Platform**: Flutter Mobile App (iOS & Android)
**Game Type**: Lottery-style betting game with numbered cards
**Backend**: Django REST API (existing)
**Architecture**: Client-Server with offline fallback

## 🎯 Core Game Mechanics

### Game Flow
1. **Setup Phase**: Player enters name, selects stake (₦200-₦1000), chooses grid size
2. **Board Generation**: Dynamic bomb placement based on stake amount
3. **Gameplay Phase**: Player flips numbered cards to avoid bombs
4. **End Conditions**: Cash out (after 2+ flips), bomb hit (game over), or perfect game

### Key Features
- **Dynamic Bomb Rates**: 3-40% based on exponential stake formula
- **Lottery-Style Cards**: Numbered 1-25 (5x5 grid) with premium fonts
- **Progressive Multipliers**: +0.05x per safe card flipped
- **Audio System**: Sound effects for flips, bombs, and wins
- **Real-time Updates**: Live bomb rate calculation and game statistics

## 🔢 Mathematical Specifications

### Dynamic Bomb Rate Algorithm
```dart
double calculateDynamicBombProbability(double stakeAmount) {
  const double minBombRate = 3.0;   // 3% minimum
  const double maxBombRate = 40.0;  // 40% maximum
  const double minStake = 200.0;    // Starting stake
  const double maxStake = 5000.0;   // Maximum progression stake

  if (stakeAmount >= maxStake) {
    return maxBombRate;
  }

  // Exponential progression calculation
  double stakeRatio = (stakeAmount - minStake) / (maxStake - minStake);
  double exponentialRatio = math.pow(stakeRatio, 1.6);
  double baseRate = minBombRate + (exponentialRatio * (maxBombRate - minBombRate));

  // Apply ±5% randomization
  Random random = Random();
  double randomVariation = (random.nextDouble() - 0.5) * 10;
  double finalRate = baseRate + randomVariation;

  // Safety bounds (1% min, 50% max)
  return math.max(1.0, math.min(50.0, finalRate));
}
```

### Board Generation Logic
```dart
class GameBoard {
  List<List<GameCell>> board = [];
  int gridSize;
  double bombRate;

  void generateBoard() {
    // Initialize grid
    for (int row = 0; row < gridSize; row++) {
      board.add([]);
      for (int col = 0; col < gridSize; col++) {
        board[row].add(GameCell(
          row: row,
          col: col,
          isBomb: false,
          isFlipped: false,
          cardNumber: (row * gridSize) + col + 1
        ));
      }
    }

    // Place bombs independently
    Random random = Random();
    int bombsPlaced = 0;

    for (int row = 0; row < gridSize; row++) {
      for (int col = 0; col < gridSize; col++) {
        if (random.nextDouble() * 100 < bombRate) {
          board[row][col].isBomb = true;
          bombsPlaced++;
        }
      }
    }
  }
}
```

### Multiplier System
```dart
class GameState {
  double currentMultiplier = 1.0;
  int safeCardsFlipped = 0;
  static const double multiplierIncrement = 0.05;

  void flipSafeCard() {
    safeCardsFlipped++;
    currentMultiplier += multiplierIncrement;
  }

  double calculateWinnings(double stake) {
    return stake * currentMultiplier;
  }
}
```

## 🎨 UI/UX Specifications

### Design Requirements
- **Theme**: Premium lottery ticket aesthetic
- **Colors**: Gold/yellow primary, red for bombs, green for safe cards
- **Typography**:
  - **Card Numbers**: Orbitron font (monospace, premium feel)
  - **Headers**: Fredoka One (playful, bold)
  - **Body**: System default (readable)

### Screen Layouts

#### 1. Game Setup Screen
```dart
// Required UI Elements
- AppBar with wallet display (₦10,000.00)
- Player name input field
- Stake amount slider/input (₦200-₦1000)
- Grid size selector (3x3 to 7x7)
- Bomb rate display (auto-calculated, real-time)
- Start Game button (disabled if insufficient funds)
```

#### 2. Game Board Screen
```dart
// Required UI Elements
- Top bar: Current stake, multiplier, potential winnings
- Game grid: Numbered cards (responsive grid)
- Bottom controls: Cash Out button, New Game button
- Sound toggle button
- Game message area (for feedback)
```

#### 3. Card Component
```dart
class GameCard extends StatefulWidget {
  final int cardNumber;
  final bool isFlipped;
  final bool isBomb;
  final VoidCallback onTap;

  // Visual States:
  // - Default: Numbered card with hover effect
  // - Flipped Safe: Green background with ✅
  // - Flipped Bomb: Red background with 💣
  // - Disabled: Grayed out (game over)
}
```

## 🔌 API Integration

### Backend Endpoints
**Base URL**: `https://flip.pbxl.cc/api`

#### 1. Start Game Session
```dart
POST /game/start/
{
  "user_id": "string",
  "username": "string",
  "starting_balance": 10000.00,
  "grid_size": 5,
  "bomb_probability": 23.5,
  "stake": 500.00
}

Response:
{
  "session_id": "uuid",
  "status": "ACTIVE"
}
```

#### 2. Log Game Events
```dart
POST /game/event/
{
  "session_id": "uuid",
  "event_type": "FLIP|CASHOUT|BOMB_HIT",
  "amount": 525.00,
  "balance": 10525.00,
  "multiplier": 1.05,
  "cell_position": "2-3"
}
```

### HTTP Service Implementation
```dart
class GameApiService {
  static const String baseUrl = 'https://flip.pbxl.cc/api';
  final Dio _dio = Dio();

  Future<GameSession> startGame(GameStartRequest request) async {
    try {
      final response = await _dio.post('$baseUrl/game/start/',
        data: request.toJson());
      return GameSession.fromJson(response.data);
    } catch (e) {
      // Handle offline mode
      return GameSession.offline();
    }
  }

  Future<void> logEvent(GameEvent event) async {
    try {
      await _dio.post('$baseUrl/game/event/',
        data: event.toJson());
    } catch (e) {
      // Queue for later sync
      await _queueOfflineEvent(event);
    }
  }
}
```

## 🎵 Audio System

### Required Sound Effects
```dart
class AudioManager {
  late AudioPlayer _audioPlayer;
  bool soundEnabled = true;

  // Required audio files:
  static const String safCardSound = 'assets/audio/ding.mp3';
  static const String bombSound = 'assets/audio/explosion.mp3';
  static const String winSound = 'assets/audio/hurray.mp3';

  Future<void> playSafeCard() async {
    if (soundEnabled) {
      await _audioPlayer.play(AssetSource(safCardSound));
    }
  }

  Future<void> playBombHit() async {
    if (soundEnabled) {
      await _audioPlayer.play(AssetSource(bombSound));
    }
  }

  Future<void> playPerfectGame() async {
    if (soundEnabled) {
      await _audioPlayer.play(AssetSource(winSound));
    }
  }
}
```

## 💾 Data Models

### Core Models
```dart
class GameCell {
  final int row;
  final int col;
  final int cardNumber;
  bool isBomb;
  bool isFlipped;

  GameCell({
    required this.row,
    required this.col,
    required this.cardNumber,
    this.isBomb = false,
    this.isFlipped = false,
  });
}

class GameSession {
  final String sessionId;
  final String userId;
  final String username;
  final double startingBalance;
  final double stake;
  final int gridSize;
  final double bombProbability;
  final String status; // ACTIVE, CASHED_OUT, BOMB_HIT

  GameSession({
    required this.sessionId,
    required this.userId,
    required this.username,
    required this.startingBalance,
    required this.stake,
    required this.gridSize,
    required this.bombProbability,
    required this.status,
  });

  factory GameSession.fromJson(Map<String, dynamic> json) {
    return GameSession(
      sessionId: json['session_id'],
      userId: json['user_id'],
      username: json['username'],
      startingBalance: json['starting_balance'].toDouble(),
      stake: json['stake'].toDouble(),
      gridSize: json['grid_size'],
      bombProbability: json['bomb_probability'].toDouble(),
      status: json['status'],
    );
  }
}

class GameState {
  double wallet;
  double currentStake;
  double currentMultiplier;
  int safeCardsFlipped;
  bool gameActive;
  bool gameOver;
  GameBoard board;

  GameState({
    this.wallet = 10000.0,
    this.currentStake = 0.0,
    this.currentMultiplier = 1.0,
    this.safeCardsFlipped = 0,
    this.gameActive = false,
    this.gameOver = false,
    required this.board,
  });
}
```

## 🎮 Game Logic Implementation

### Game Controller
```dart
class GameController extends ChangeNotifier {
  GameState _gameState = GameState(board: GameBoard(5));
  GameApiService _apiService = GameApiService();
  AudioManager _audioManager = AudioManager();
  String? _currentSessionId;

  // Getters
  GameState get gameState => _gameState;
  bool get canCashOut => _gameState.safeCardsFlipped >= 2;
  double get potentialWinnings => _gameState.currentStake * _gameState.currentMultiplier;

  // Start new game
  Future<void> startGame({
    required String username,
    required double stake,
    required int gridSize,
  }) async {
    // Validate stake
    if (stake < 200 || stake > _gameState.wallet) {
      throw Exception('Invalid stake amount');
    }

    // Calculate dynamic bomb rate
    double bombRate = calculateDynamicBombProbability(stake);

    // Initialize game state
    _gameState.currentStake = stake;
    _gameState.wallet -= stake;
    _gameState.currentMultiplier = 1.0;
    _gameState.safeCardsFlipped = 0;
    _gameState.gameActive = true;
    _gameState.gameOver = false;
    _gameState.board = GameBoard(gridSize);
    _gameState.board.bombRate = bombRate;
    _gameState.board.generateBoard();

    // Start backend session
    try {
      final session = await _apiService.startGame(GameStartRequest(
        userId: _generateUserId(),
        username: username,
        startingBalance: _gameState.wallet + stake,
        gridSize: gridSize,
        bombProbability: bombRate,
        stake: stake,
      ));
      _currentSessionId = session.sessionId;
    } catch (e) {
      // Continue in offline mode
      print('Playing offline: $e');
    }

    notifyListeners();
  }

  // Handle card flip
  Future<void> flipCard(int row, int col) async {
    if (!_gameState.gameActive || _gameState.gameOver) return;

    GameCell cell = _gameState.board.board[row][col];
    if (cell.isFlipped) return;

    // Flip the card
    cell.isFlipped = true;

    if (cell.isBomb) {
      // Bomb hit - game over
      _gameState.gameOver = true;
      _gameState.gameActive = false;

      await _audioManager.playBombHit();
      _revealAllBombs();

      // Log bomb hit event
      await _logEvent(GameEvent(
        sessionId: _currentSessionId,
        eventType: 'BOMB_HIT',
        balance: _gameState.wallet,
        multiplier: _gameState.currentMultiplier,
        cellPosition: '${row}-${col}',
      ));

    } else {
      // Safe card
      _gameState.safeCardsFlipped++;
      _gameState.currentMultiplier += 0.05;

      await _audioManager.playSafeCard();

      // Check for perfect game
      if (_gameState.safeCardsFlipped == _getTotalSafeCells()) {
        await _handlePerfectGame();
      }

      // Log flip event
      await _logEvent(GameEvent(
        sessionId: _currentSessionId,
        eventType: 'FLIP',
        balance: _gameState.wallet,
        multiplier: _gameState.currentMultiplier,
        cellPosition: '${row}-${col}',
      ));
    }

    notifyListeners();
  }

  // Cash out
  Future<void> cashOut() async {
    if (!canCashOut) {
      throw Exception('Need at least 2 safe cards to cash out');
    }

    double winnings = potentialWinnings;
    _gameState.wallet += winnings;
    _gameState.gameActive = false;

    // Log cashout event
    await _logEvent(GameEvent(
      sessionId: _currentSessionId,
      eventType: 'CASHOUT',
      amount: winnings,
      balance: _gameState.wallet,
      multiplier: _gameState.currentMultiplier,
    ));

    notifyListeners();
  }

  // Reset game
  void resetGame() {
    _gameState.currentStake = 0;
    _gameState.currentMultiplier = 1.0;
    _gameState.safeCardsFlipped = 0;
    _gameState.gameActive = false;
    _gameState.gameOver = false;
    _currentSessionId = null;
    notifyListeners();
  }

  // Private helper methods
  void _revealAllBombs() {
    for (var row in _gameState.board.board) {
      for (var cell in row) {
        if (cell.isBomb) {
          cell.isFlipped = true;
        }
      }
    }
  }

  int _getTotalSafeCells() {
    int safeCells = 0;
    for (var row in _gameState.board.board) {
      for (var cell in row) {
        if (!cell.isBomb) safeCells++;
      }
    }
    return safeCells;
  }

  Future<void> _handlePerfectGame() async {
    await _audioManager.playPerfectGame();
    await cashOut();
  }

  Future<void> _logEvent(GameEvent event) async {
    try {
      await _apiService.logEvent(event);
    } catch (e) {
      print('Failed to log event: $e');
    }
  }

  String _generateUserId() {
    return 'flutter_${DateTime.now().millisecondsSinceEpoch}';
  }
}
```

## 📱 Required Flutter Packages

### pubspec.yaml Dependencies
```yaml
dependencies:
  flutter:
    sdk: flutter

  # State Management
  provider: ^6.0.5

  # HTTP & API
  dio: ^5.3.2

  # Audio
  audioplayers: ^5.2.1

  # UI Components
  flutter_screenutil: ^5.9.0  # Responsive design
  google_fonts: ^6.1.0        # Custom fonts

  # Storage
  shared_preferences: ^2.2.2  # Local storage

  # Utilities
  uuid: ^4.1.0                # Generate unique IDs

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
```

## 🎨 Asset Requirements

### Directory Structure
```
assets/
├── audio/
│   ├── ding.mp3          # Safe card sound
│   ├── explosion.mp3     # Bomb hit sound
│   └── hurray.mp3        # Perfect game sound
├── fonts/
│   ├── Orbitron-Regular.ttf
│   ├── Orbitron-Bold.ttf
│   └── FredokaOne-Regular.ttf
└── images/
    ├── app_icon.png
    └── splash_logo.png
```

### Font Configuration
```yaml
# pubspec.yaml
flutter:
  fonts:
    - family: Orbitron
      fonts:
        - asset: assets/fonts/Orbitron-Regular.ttf
        - asset: assets/fonts/Orbitron-Bold.ttf
          weight: 700
    - family: FredokaOne
      fonts:
        - asset: assets/fonts/FredokaOne-Regular.ttf
```

## 🔧 Configuration & Constants

### App Configuration
```dart
class AppConfig {
  // Game Settings
  static const double startingWallet = 10000.0;
  static const double minStake = 200.0;
  static const double maxStake = 1000.0;
  static const double multiplierIncrement = 0.05;
  static const int minFlipsForCashout = 2;

  // API Settings
  static const String apiBaseUrl = 'https://flip.pbxl.cc/api';
  static const Duration apiTimeout = Duration(seconds: 10);

  // UI Settings
  static const List<int> availableGridSizes = [3, 4, 5, 6, 7];
  static const int defaultGridSize = 5;

  // Colors
  static const Color primaryColor = Color(0xFFFFD700);    // Gold
  static const Color bombColor = Color(0xFFE74C3C);       // Red
  static const Color safeColor = Color(0xFF27AE60);       // Green
  static const Color backgroundColor = Color(0xFF2C3E50);  // Dark blue
}
```

## 🧪 Testing Requirements

### Unit Tests
```dart
// test/game_logic_test.dart
void main() {
  group('Bomb Rate Calculation', () {
    test('should return 3% for minimum stake', () {
      double rate = calculateDynamicBombProbability(200);
      expect(rate, greaterThanOrEqualTo(1.0));
      expect(rate, lessThanOrEqualTo(8.0)); // 3% ±5%
    });

    test('should cap at 40% for high stakes', () {
      double rate = calculateDynamicBombProbability(6000);
      expect(rate, lessThanOrEqualTo(50.0)); // Safety bound
    });
  });

  group('Game State Management', () {
    test('should prevent cashout before 2 flips', () {
      GameController controller = GameController();
      expect(controller.canCashOut, false);
    });

    test('should calculate winnings correctly', () {
      GameController controller = GameController();
      controller.gameState.currentStake = 1000;
      controller.gameState.currentMultiplier = 1.25;
      expect(controller.potentialWinnings, 1250.0);
    });
  });
}
```

## 📋 Development Checklist

### Phase 1: Core Implementation ⏱️ (Week 1-2)
- [ ] Set up Flutter project with required dependencies
- [ ] Implement data models (GameCell, GameState, GameSession)
- [ ] Create bomb rate calculation algorithm
- [ ] Build board generation logic
- [ ] Implement game controller with state management

### Phase 2: UI Development ⏱️ (Week 2-3)
- [ ] Design game setup screen with stake input
- [ ] Create responsive game board grid
- [ ] Implement numbered card components with animations
- [ ] Add game controls (cash out, new game buttons)
- [ ] Integrate custom fonts (Orbitron, Fredoka One)

### Phase 3: Audio & Effects ⏱️ (Week 3)
- [ ] Integrate audio player package
- [ ] Add sound effects for card flips, bombs, wins
- [ ] Implement sound toggle functionality
- [ ] Add haptic feedback for card taps

### Phase 4: API Integration ⏱️ (Week 4)
- [ ] Implement HTTP service for backend communication
- [ ] Add offline mode with local storage
- [ ] Implement event logging system
- [ ] Add error handling and retry logic

### Phase 5: Polish & Testing ⏱️ (Week 5)
- [ ] Add animations and visual effects
- [ ] Implement responsive design for different screen sizes
- [ ] Write unit tests for game logic
- [ ] Perform integration testing
- [ ] Optimize performance and memory usage

## 🚀 Deployment Considerations

### Build Configuration
```dart
// android/app/build.gradle
android {
    compileSdkVersion 34

    defaultConfig {
        applicationId "com.yourcompany.bombflip"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 1
        versionName "1.0.0"
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt')
        }
    }
}

// ios/Runner/Info.plist
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>
<key>UIRequiredDeviceCapabilities</key>
<array>
    <string>arm64</string>
</array>
```

### Performance Optimization
- Use `const` constructors for static widgets
- Implement proper disposal of controllers and streams
- Optimize image and audio asset sizes
- Use `ListView.builder` for large grids if needed
- Implement proper error boundaries

### Security Considerations
- Validate all user inputs on both client and server
- Implement proper session management
- Use HTTPS for all API communications
- Store sensitive data securely using Flutter Secure Storage
- Implement proper authentication if required

## 📊 Key Metrics to Track

### Game Analytics
- **Session Duration**: Average time per game
- **Stake Distribution**: Most popular stake amounts
- **Cashout Patterns**: When players typically cash out
- **Bomb Hit Rate**: Actual vs expected bomb encounters
- **Perfect Game Rate**: Frequency of complete wins

### Technical Metrics
- **API Response Times**: Backend performance monitoring
- **Offline Mode Usage**: How often users play offline
- **Crash Rate**: App stability metrics
- **Battery Usage**: Performance optimization tracking

## 🎯 Success Criteria

### Functional Requirements ✅
- [ ] Dynamic bomb rate calculation working correctly
- [ ] Smooth card flip animations and interactions
- [ ] Audio system functioning on all devices
- [ ] Backend integration with offline fallback
- [ ] Responsive design across screen sizes

### Performance Requirements ✅
- [ ] App launches in under 3 seconds
- [ ] Card flip response time under 100ms
- [ ] Memory usage stays under 100MB
- [ ] Battery drain minimal during gameplay
- [ ] No crashes during normal gameplay

### User Experience Requirements ✅
- [ ] Intuitive game flow and controls
- [ ] Clear visual feedback for all actions
- [ ] Accessible design following Flutter guidelines
- [ ] Smooth animations and transitions
- [ ] Proper error handling and user feedback

---

## 📞 Development Support

### Technical Questions
- **Algorithm Implementation**: Reference the mathematical formulas provided
- **API Integration**: Use the detailed endpoint specifications
- **UI Components**: Follow the design requirements and asset specifications
- **Testing**: Implement the provided unit test examples

### Resources
- **Flutter Documentation**: https://flutter.dev/docs
- **Provider State Management**: https://pub.dev/packages/provider
- **Audio Players**: https://pub.dev/packages/audioplayers
- **HTTP Requests**: https://pub.dev/packages/dio

---

**This comprehensive specification provides everything needed to build the Bomb Flip Betting Game in Flutter. All algorithms, UI requirements, API integrations, and deployment considerations are included for successful mobile app development.**

**Estimated Development Time: 4-5 weeks for a complete, production-ready Flutter application.**
