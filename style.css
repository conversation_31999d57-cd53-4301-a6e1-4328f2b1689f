* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.container {
    background: rgba(255, 255, 255, 0.98);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    max-width: 900px;
    width: 100%;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

h1 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 2.8em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    font-weight: bold;
}

/* Wallet Section */
.wallet-section {
    margin-bottom: 25px;
}

.wallet-display {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
    color: white;
    padding: 15px 25px;
    border-radius: 50px;
    display: inline-block;
    font-size: 1.3em;
    font-weight: bold;
    box-shadow: 0 8px 25px rgba(46, 204, 113, 0.3);
    margin-bottom: 10px;
}

.wallet-label {
    margin-right: 10px;
}

.winnings-display {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
    padding: 12px 20px;
    border-radius: 50px;
    display: inline-block;
    font-size: 1.1em;
    font-weight: bold;
    box-shadow: 0 6px 20px rgba(243, 156, 18, 0.3);
    animation: winningsAppear 0.8s ease-out;
}

.winnings-label {
    margin-right: 8px;
}

@keyframes winningsAppear {
    0% {
        opacity: 0;
        transform: translateY(-20px) scale(0.8);
    }
    50% {
        transform: translateY(-5px) scale(1.1);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Game Setup Styles */
.game-setup {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 25px;
    border: 2px solid #e9ecef;
}

.setup-row {
    display: flex;
    justify-content: center;
    gap: 25px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.input-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 150px;
}

.input-group label {
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 0.9em;
}

.input-group input,
.input-group select {
    padding: 10px 15px;
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    font-size: 16px;
    text-align: center;
    background: white;
    transition: border-color 0.3s ease;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.input-group input[type="text"] {
    text-align: left;
    font-weight: bold;
    color: #2c3e50;
}

.input-group input[type="text"]::placeholder {
    color: #95a5a6;
    font-weight: normal;
}

/* Game Info Styles */
.game-info {
    background: #ecf0f1;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 25px;
    border: 2px solid #bdc3c7;
}

.info-row {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.info-item {
    background: white;
    padding: 12px 20px;
    border-radius: 25px;
    border: 2px solid #d5dbdb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-width: 120px;
    text-align: center;
}

.label {
    font-weight: bold;
    color: #2c3e50;
    margin-right: 8px;
    font-size: 0.9em;
}

/* Player Info Styles */
.player-info {
    text-align: center;
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    margin-bottom: 15px;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
}

.player-label {
    margin-right: 8px;
}

#current-player {
    font-size: 1.1em;
    text-transform: capitalize;
}

.game-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

/* Button Styles */
.btn {
    border: none;
    padding: 14px 28px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
}

.btn-success {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
    color: white;
}

.btn-secondary {
    background: linear-gradient(45deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn:disabled:hover {
    transform: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Sound Control */
.sound-control {
    margin-top: 10px;
    text-align: center;
}

.btn-sound {
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-sound:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn-sound.muted {
    background: linear-gradient(45deg, #95a5a6, #7f8c8d);
}

/* Game Board Styles */
.game-board {
    display: grid;
    gap: 3px;
    justify-content: center;
    margin: 20px auto;
    background: #34495e;
    padding: 15px;
    border-radius: 15px;
    box-shadow: inset 0 0 25px rgba(0, 0, 0, 0.4);
    border: 3px solid #2c3e50;
}

.card {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #f39c12, #e67e22);
    border: 3px solid #fff;
    border-radius: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 20px;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.card:hover:not(.flipped) {
    transform: scale(1.08) rotateZ(2deg);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
    background: linear-gradient(45deg, #e67e22, #d35400);
}

.card.flipped {
    background: #ecf0f1;
    color: #2c3e50;
    cursor: default;
    transform: rotateY(180deg);
    animation: flip 0.6s ease-in-out;
    border-color: #bdc3c7;
}

.card.safe {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    color: white;
    font-size: 24px;
}

.card.bomb {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    font-size: 28px;
    animation: explode 0.8s ease-out;
}

.card.bomb-revealed {
    background: linear-gradient(45deg, #95a5a6, #7f8c8d);
    color: #2c3e50;
}

/* Animations */
@keyframes flip {
    0% { transform: rotateY(0deg); }
    50% { transform: rotateY(90deg) scale(1.1); }
    100% { transform: rotateY(180deg); }
}

@keyframes explode {
    0% { transform: scale(1); }
    50% { transform: scale(1.3) rotate(10deg); }
    100% { transform: scale(1) rotate(0deg); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.game-message {
    text-align: center;
    padding: 25px;
    margin: 25px 0;
    border-radius: 15px;
    font-size: 28px;
    font-weight: bold;
    animation: slideIn 0.6s ease-out;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.game-message.win {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
    color: white;
    border: 3px solid #1e8449;
}

.game-message.lose {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    border: 3px solid #a93226;
}

.game-message.cashout {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
    border: 3px solid #d68910;
}

.hidden {
    display: none;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 5px;
    }

    h1 {
        font-size: 2em;
        margin-bottom: 15px;
    }

    .wallet-section {
        margin-bottom: 15px;
    }

    .wallet-display {
        font-size: 1.1em;
        padding: 12px 20px;
        margin-bottom: 8px;
    }

    .winnings-display {
        font-size: 1em;
        padding: 10px 16px;
    }

    .setup-row {
        flex-direction: column;
        gap: 12px;
        margin-bottom: 15px;
    }

    .input-group {
        min-width: 180px;
    }

    .input-group label {
        font-size: 0.85em;
        margin-bottom: 6px;
    }

    .input-group input,
    .input-group select {
        padding: 8px 12px;
        font-size: 14px;
    }

    /* Mobile-specific game info layout - 2x2 grid */
    .info-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
        gap: 10px;
        margin-bottom: 15px;
    }

    .info-item {
        padding: 8px 12px;
        font-size: 12px;
        min-width: auto;
        text-align: center;
    }

    .info-item .label {
        display: block;
        font-size: 0.75em;
        margin-bottom: 2px;
        margin-right: 0;
    }

    /* Mobile game controls - side by side */
    .game-controls {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        margin-top: 10px;
    }

    .btn {
        width: 100%;
        padding: 12px 16px;
        font-size: 14px;
        max-width: none;
    }

    .card {
        width: 42px;
        height: 42px;
        font-size: 16px;
    }

    .game-board {
        padding: 10px;
        margin: 15px auto;
    }

    .game-message {
        font-size: 18px;
        padding: 15px;
        margin: 15px 0;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
        margin: 2px;
    }

    h1 {
        font-size: 1.6em;
        margin-bottom: 12px;
    }

    .wallet-display {
        font-size: 1em;
        padding: 10px 16px;
    }

    .winnings-display {
        font-size: 0.9em;
        padding: 8px 14px;
    }

    .game-setup {
        padding: 15px;
    }

    .game-info {
        padding: 15px;
    }

    .info-item {
        padding: 6px 8px;
        font-size: 11px;
    }

    .info-item .label {
        font-size: 0.7em;
    }

    .btn {
        padding: 10px 12px;
        font-size: 13px;
    }

    .card {
        width: 38px;
        height: 38px;
        font-size: 15px;
    }

    .game-board {
        padding: 8px;
        margin: 10px auto;
    }

    .game-message {
        font-size: 16px;
        padding: 12px;
        margin: 10px 0;
    }

    /* Ensure game board takes more screen space on very small screens */
    header {
        margin-bottom: 15px;
    }
}
