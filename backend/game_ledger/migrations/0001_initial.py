# Generated by Django 4.2.23 on 2025-08-29 14:42

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="GameSession",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "user_id",
                    models.CharField(
                        help_text="Identifier for the player", max_length=100
                    ),
                ),
                (
                    "starting_balance",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Player's wallet balance at game start",
                        max_digits=12,
                    ),
                ),
                (
                    "stake",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Amount wagered for this game",
                        max_digits=12,
                    ),
                ),
                (
                    "grid_size",
                    models.IntegerField(
                        help_text="Size of the game grid (e.g., 5 for 5x5)"
                    ),
                ),
                (
                    "bomb_probability",
                    models.Float<PERSON>ield(
                        help_text="Probability of bombs as percentage (e.g., 20.0 for 20%)"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("ACTIVE", "Active"),
                            ("CASHED_OUT", "Cashed Out"),
                            ("BOMB_HIT", "Bomb Hit"),
                        ],
                        default="ACTIVE",
                        help_text="Current status of the game session",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "ended_at",
                    models.DateTimeField(
                        blank=True, help_text="When the game ended", null=True
                    ),
                ),
            ],
            options={
                "verbose_name": "Game Session",
                "verbose_name_plural": "Game Sessions",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="GameEvent",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "event_type",
                    models.CharField(
                        choices=[
                            ("GAME_STARTED", "Game Started"),
                            ("FLIP", "Card Flip"),
                            ("CASHOUT", "Cash Out"),
                            ("BOMB_HIT", "Bomb Hit"),
                        ],
                        help_text="Type of event that occurred",
                        max_length=20,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Amount involved in the event (winnings, stake, etc.)",
                        max_digits=12,
                        null=True,
                    ),
                ),
                (
                    "balance",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Player's balance after this event",
                        max_digits=12,
                        null=True,
                    ),
                ),
                (
                    "multiplier",
                    models.FloatField(
                        blank=True,
                        help_text="Current multiplier at time of event",
                        null=True,
                    ),
                ),
                (
                    "cell_position",
                    models.CharField(
                        blank=True,
                        help_text="Grid position of flipped card (e.g., '2-3')",
                        max_length=10,
                        null=True,
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "session",
                    models.ForeignKey(
                        help_text="The game session this event belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="events",
                        to="game_ledger.gamesession",
                    ),
                ),
            ],
            options={
                "verbose_name": "Game Event",
                "verbose_name_plural": "Game Events",
                "ordering": ["timestamp"],
            },
        ),
    ]
