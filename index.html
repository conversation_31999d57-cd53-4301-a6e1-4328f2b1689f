<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💣 Bomb Flip Betting Game 💰</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Fredoka+One&family=Righteous&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>💣 Bomb Flip Betting Game �</h1>

            <!-- Wallet Display -->
            <div class="wallet-section">
                <div class="wallet-display">
                    <span class="wallet-label">💰 Wallet:</span>
                    <span id="wallet-amount">₦10000.00</span>
                </div>
                <div class="winnings-display hidden" id="winnings-display">
                    <span class="winnings-label">🎉 Last Win:</span>
                    <span id="last-winnings">₦0.00</span>
                </div>
            </div>

            <!-- Game Setup -->
            <div class="game-setup" id="game-setup">
                <div class="setup-row">
                    <div class="input-group">
                        <label for="username-input">Player Name:</label>
                        <input type="text" id="username-input" placeholder="Enter your name" maxlength="20" value="">
                    </div>
                    <div class="input-group">
                        <label for="stake-input">Stake Amount (₦):</label>
                        <input type="number" id="stake-input" min="200" max="1000" value="200" step="1">
                    </div>
                    <div class="input-group">
                        <label for="grid-size">Grid Size:</label>
                        <select id="grid-size">
                            <option value="3">3x3</option>
                            <option value="4">4x4</option>
                            <option value="5" selected>5x5</option>
                            <option value="6">6x6</option>
                            <option value="7">7x7</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label for="bomb-info">Bomb Rate:</label>
                        <div id="bomb-info" class="bomb-info-display">
                            <span id="bomb-rate-text">Auto-calculated</span>
                        </div>
                    </div>
                </div>
                <button id="start-game-btn" class="btn btn-primary">Start Game</button>
            </div>

            <!-- Game Info (shown during game) -->
            <div class="game-info hidden" id="game-info">
                <div class="player-info">
                    <span class="player-label">🎮 Player:</span>
                    <span id="current-player">Guest</span>
                </div>
                <div class="info-row">
                    <div class="info-item">
                        <span class="label">Stake:</span>
                        <span id="current-stake">₦0.00</span>
                    </div>
                    <div class="info-item">
                        <span class="label">Multiplier:</span>
                        <span id="current-multiplier">1.00x</span>
                    </div>
                    <div class="info-item">
                        <span class="label">Potential Winnings:</span>
                        <span id="potential-winnings">₦0.00</span>
                    </div>
                    <div class="info-item">
                        <span class="label">Safe Cards:</span>
                        <span id="safe-cards">0</span>
                    </div>
                </div>
                <div class="game-controls">
                    <button id="cashout-btn" class="btn btn-success">💰 Cash Out</button>
                    <button id="reset-game-btn" class="btn btn-secondary">🔄 New Game</button>
                </div>
                <div class="sound-control">
                    <button id="sound-toggle-btn" class="btn-sound" title="Toggle Sound">🔊</button>
                </div>
            </div>
        </header>

        <main>
            <div id="game-board" class="game-board"></div>
            <div id="game-message" class="game-message"></div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
